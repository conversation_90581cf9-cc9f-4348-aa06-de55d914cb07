extends ConveyorBasedBuilding

class_name SimpleProcessingBuilding

var is_set := false
var recipe: ItemRecipe
var new_item_data: ItemData

var total_processing_time: float


@onready var power_receiver_component: PowerReceiverComponent = $Components/PowerReceiverComponent
@onready var animated_sprite_2d: AnimatedSprite2D = $AnimatedSprite2D


func _ready() -> void:
	power_receiver_component.power_received.connect(_power_received)


func _process(_delta: float) -> void:
	if Engine.is_editor_hint():
		return

	# Wait for item
	if _held_item == null:
		return
		
	# Wait for animation to finish (item to arrive)
	if _held_item.is_moving():
		return
	
	if not is_set:
		is_set = true
		total_processing_time = 0
		
		# Get recipe if there is any for this item
		recipe = UnlockManager.get_simple_recipe(stats.building_type, _held_item.item_data)
		if recipe:
			new_item_data = recipe.output_resources.keys().front()

	# Process item
	if recipe:
		if total_processing_time < recipe.processing_time:
			power_receiver_component.efficiency = 1
			if not animated_sprite_2d.is_playing():
				animated_sprite_2d.play(animated_sprite_2d.animation)
			return
	
	power_receiver_component.efficiency = 0

	# Wait for output to be available
	if output_buildings.is_empty():
		return
	
	# Perform operation on item
	if recipe:
		_held_item.item_data = new_item_data
		_held_item.reload_stats()
	
	# Release item
	reset()
	push_item.emit(_held_item, self)


func reset():
	is_set = false
	recipe = null
	new_item_data = null


func _power_received(power: Variant, delta: Variant) -> void:
	if power == 0:
		return

	# Does not have item but recieved some power
	assert(_held_item)

	var work_done = (power / power_receiver_component.power_amount) * delta
	total_processing_time += work_done


## Save building state for persistence
func save_data() -> Dictionary:
	var save_dict = {}

	# Save processing state
	save_dict["is_set"] = is_set
	save_dict["total_processing_time"] = total_processing_time

	# Save recipe if exists
	if recipe:
		save_dict["recipe_path"] = recipe.resource_path

	# Save new item data if exists
	if new_item_data:
		save_dict["new_item_data_path"] = new_item_data.resource_path

	# Save held item if exists
	if _held_item and _held_item.item_data:
		save_dict["held_item"] = {
			"item_data_path": _held_item.item_data.resource_path,
			"temperature": _held_item.temperature
		}

	return save_dict


## Load building state from persistence
func load_data(data: Dictionary) -> void:
	# Load processing state
	if data.has("is_set"):
		is_set = data["is_set"]
	if data.has("total_processing_time"):
		total_processing_time = data["total_processing_time"]

	# Load recipe
	if data.has("recipe_path") and not data["recipe_path"].is_empty():
		recipe = load(data["recipe_path"]) as ItemRecipe

	# Load new item data
	if data.has("new_item_data_path") and not data["new_item_data_path"].is_empty():
		new_item_data = load(data["new_item_data_path"]) as ItemData

	# Load held item
	if data.has("held_item"):
		var held_item_data = data["held_item"]
		if held_item_data.has("item_data_path"):
			var item_resource = load(held_item_data["item_data_path"]) as ItemData
			if item_resource:
				_held_item = _instantiate_item(item_resource)
				if held_item_data.has("temperature"):
					_held_item.temperature = held_item_data["temperature"]


## Get unique identifier for this component type
func get_save_id() -> String:
	return "SimpleProcessingBuilding"
