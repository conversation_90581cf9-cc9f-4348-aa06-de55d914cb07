class_name Assembler
extends ItemHandlingBuilding

# Temporarly hard coded things since i dont have access to inserters
# TODO remove hard coded after inserters exist
@export var available_resources: Dictionary[ItemType.Enum, int] = {
	ItemType.Enum.IRON_PLATE: 10,
	ItemType.Enum.COAL: 10,
}
@export var outputs: Array[Item]
var items_to_clear: Array[Item]

@export var recipe: ItemRecipe
var _is_crafting: bool = false
var _current_crafting_time: float = 0.0

@onready var power_receiver_component: PowerReceiverComponent = $Components/PowerReceiverComponent

var _incoming_items: Array[Item] = []

func _ready() -> void:
	# TODO change this once recipe select exists
	var array = UnlockManager.get_complex_recipes(BuildingType.Enum.ASSEMBLER)
	if array.size() == 0:
		recipe = null
	else:
		recipe = array[0]
	power_receiver_component.power_received.connect(_power_received)


func reserve(other_building: Building) -> void:
	pass


func _on_push_item(item: Item, _building: Building):
	if item == null:
		return
	item_received.emit(item, self)


func _on_item_transport_confirm(item: Item, building: Building) -> void:
	if building == self:
		if item.item_data.type not in available_resources:
			available_resources[item.item_data.type] = 0
		item.reparent(building)
		_incoming_items.append(item)


func _on_item_received(item: Item, building: Building) -> void:
	if item in outputs and item not in items_to_clear:
		item.global_position = (building as Inserter).arm_component.attach_point.global_position
		item_transport_confirmed.emit(item, building)
		items_to_clear.append(item)


func _after_process(_delta: float) -> void:
	for item in items_to_clear:
		outputs.erase(item)
	items_to_clear.clear()
	# Waits for animations to finish before deleting items
	var i = 0
	while i < _incoming_items.size():
		var item: Item = _incoming_items[i]
		if not item.is_moving():
			available_resources[item.item_data.type] += 1
			item.queue_free()
			item = null
			_incoming_items.remove_at(i)
			continue
		i += 1
	
	if recipe == null:
		return
	
	if _is_crafting:
		if _current_crafting_time < recipe.processing_time:
			power_receiver_component.efficiency = 1
		else:
			_consume_resources()
			_generate_items()
			_is_crafting = false
			_current_crafting_time = 0.0
			power_receiver_component.efficiency = 0
		return
	
	for key in recipe.input_resources.keys():
		if available_resources.get(key.type, -1) < recipe.input_resources[key]:
			return
	
	_is_crafting = true


func _generate_items() -> void:
	for item_data in recipe.output_resources.keys():
		outputs.append(_instantiate_item(item_data))


func _consume_resources() -> void:
	for item_data in recipe.input_resources.keys():
		available_resources[item_data.type] -= recipe.input_resources[item_data]


func get_output_items() -> Array[Item]:
	return outputs


func _power_received(power: Variant, delta: Variant) -> void:
	if not _is_crafting:
		return
	
	# Not sure about this value it was copied from smeltery
	var provided_crafting = (power / power_receiver_component.power_amount) * delta
	_current_crafting_time += provided_crafting
