[gd_scene load_steps=8 format=3 uid="uid://cdiynhno0rg0o"]

[ext_resource type="Shader" uid="uid://bcwxrw8dp8eru" path="res://Shaders/Blurr.gdshader" id="1_ey1tb"]
[ext_resource type="Theme" uid="uid://nb8ct3v6e2ln" path="res://assets/Styles/base_theme.tres" id="3_rqyw7"]
[ext_resource type="Script" uid="uid://bh8ydbhr8yri6" path="res://Scenes/Menus/pause_menu.gd" id="1_siy43"]
[ext_resource type="StyleBox" uid="uid://8en3eownniur" path="res://Assets/Styles/GenericButtonHovered.tres" id="2_siy43"]
[ext_resource type="StyleBox" uid="uid://br1jxnl5ox4pr" path="res://Assets/Styles/GenericButtonClicked.tres" id="3_yxrls"]
[ext_resource type="StyleBox" uid="uid://byicfcubdmq4h" path="res://Assets/Styles/GenericButton.tres" id="4_gqxqw"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_lptsj"]
shader = ExtResource("1_ey1tb")
shader_parameter/lod = 0.0

[sub_resource type="Animation" id="Animation_gqxqw"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("ColorRect:material:shader_parameter/lod")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Panel:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 0)]
}

[sub_resource type="Animation" id="Animation_yxrls"]
resource_name = "blur"
length = 0.3
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("ColorRect:material:shader_parameter/lod")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 0.835]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Panel:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_lptsj"]
_data = {
&"RESET": SubResource("Animation_gqxqw"),
&"blur": SubResource("Animation_yxrls")
}

[node name="PauseMenu" type="Control"]
process_mode = 3
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_siy43")

[node name="ColorRect" type="ColorRect" parent="."]
material = SubResource("ShaderMaterial_lptsj")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Panel" parent="."]
modulate = Color(1, 1, 1, 0)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -320.0
offset_top = -363.0
offset_right = 320.0
offset_bottom = 363.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Resume" type="Button" parent="Panel/VBoxContainer"]
custom_minimum_size = Vector2(500, 100)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 6
theme = ExtResource("3_rqyw7")
text = "Resume"

[node name="BackToPlanetSelect" type="Button" parent="Panel/VBoxContainer"]
custom_minimum_size = Vector2(500, 100)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 6
theme = ExtResource("3_rqyw7")
text = "Back to planet select"

[node name="Quit" type="Button" parent="Panel/VBoxContainer"]
custom_minimum_size = Vector2(500, 100)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 6
theme = ExtResource("3_rqyw7")
text = "Quit"

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_lptsj")
}

[connection signal="pressed" from="Panel/VBoxContainer/Resume" to="." method="_on_resume_pressed"]
[connection signal="pressed" from="Panel/VBoxContainer/BackToPlanetSelect" to="." method="_on_back_to_planet_select_pressed"]
[connection signal="pressed" from="Panel/VBoxContainer/Quit" to="." method="_on_quit_pressed"]
