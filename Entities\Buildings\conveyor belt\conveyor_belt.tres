[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=6 format=3 uid="uid://dt84gufsp5ag6"]

[ext_resource type="SpriteFrames" uid="uid://dmuauo4oj028m" path="res://Assets/Sprites/Animations/32x32/Conveyor.tres" id="1_uv56o"]
[ext_resource type="Script" uid="uid://wc3xoylk0uc3" path="res://Entities/Buildings/conveyor belt/conveyor_belt.gd" id="2_xm3n8"]
[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="3_dgrpd"]
[ext_resource type="Texture2D" uid="uid://bnhcqxfsi32ec" path="res://Assets/Sprites/32x32/SpriteSheets/Simpleconveyor_sprite_sheet_anim.png" id="4_qahpd"]

[sub_resource type="AtlasTexture" id="AtlasTexture_q1wsc"]
atlas = ExtResource("4_qahpd")
region = Rect2(0, 64, 32, 32)

[resource]
resource_name = "Conveyor Belt"
script = ExtResource("3_dgrpd")
input_directions = 14
output_directions = 1
transport_speed = 0.0
translation_key = "CONVEYOR_BELT"
dimensions = Vector2i(1, 1)
is_rotateable = true
building_type = 1
components = Array[Resource]([])
cost = Dictionary[int, int]({})
texture = SubResource("AtlasTexture_q1wsc")
animation_frames = ExtResource("1_uv56o")
building_script = ExtResource("2_xm3n8")
unlocked_by_default = true
menu_order_priority = -1
metadata/_custom_type_script = "uid://qrdti4s4su0t"
