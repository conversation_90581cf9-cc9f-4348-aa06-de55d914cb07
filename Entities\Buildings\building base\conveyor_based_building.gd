## Represents buildigns that are part of the conveyor network.
class_name ConveyorBasedBuilding
extends ItemHandlingBuilding


## Maps direction to a vector in dependences to the rotation.
static func vector_from_direction_per_rotation_degrees(direction: IODirection.Enum, degrees: float):
	# assert (degrees in [0, 90, 180, 270])

	# Dumb and simple code, it can't get better than this
	if is_equal_approx(degrees, 0.0):
		match direction:
			IODirection.Enum.FRONT: return Vector2i(1, 0)
			IODirection.Enum.BACK: return Vector2i(-1, 0)
			IODirection.Enum.LEFT: return Vector2i(0, -1)
			IODirection.Enum.RIGHT: return Vector2i(0, 1)
	elif is_equal_approx(degrees, 90.0):
		match direction:
			IODirection.Enum.FRONT: return Vector2i(0, 1)
			IODirection.Enum.BACK: return Vector2i(0, -1)
			IODirection.Enum.LEFT: return Vector2i(1, 0)
			IODirection.Enum.RIGHT: return Vector2i(-1, 0)
	elif is_equal_approx(degrees, 180.0):
		match direction:
			IODirection.Enum.FRONT: return Vector2i(-1, 0)
			IODirection.Enum.BACK: return Vector2i(1, 0)
			IODirection.Enum.LEFT: return Vector2i(0, 1)
			IODirection.Enum.RIGHT: return Vector2i(0, -1)
	elif is_equal_approx(degrees, 270.0):
		match direction:
			IODirection.Enum.FRONT: return Vector2i(0, -1)
			IODirection.Enum.BACK: return Vector2i(0, 1)
			IODirection.Enum.LEFT: return Vector2i(-1, 0)
			IODirection.Enum.RIGHT: return Vector2i(1, 0)


# Building that are part of the conveyor network do not have side products, thus they can hold
# only a single item at a particular moment.
@export var _held_item: Item = null:
	set(new_value):
		if new_value == null:
			is_occupied = false
		else:
			is_occupied = true
		_held_item = new_value


## Gets output tile coordinates for this building.
func get_output_tile_coordinates() -> Array[Vector2i]:
	var output_tile_coordinates: Array[Vector2i]
	
	if rotation_degrees < 0.0:
		rotation_degrees += 360.0
	
	for output_direction in IODirection.directions_from_flags(stats.output_directions):
		output_tile_coordinates.append(
			tile_coordinates.front() + vector_from_direction_per_rotation_degrees(
				output_direction,
				rotation_degrees
			)
		)
	return output_tile_coordinates


## Gets input tile coordinates for this building.
func get_input_tile_coordinates() -> Array[Vector2i]:
	var input_tile_coordinates: Array[Vector2i]
	for input_direction in IODirection.directions_from_flags(stats.input_directions):
		input_tile_coordinates.append(
			tile_coordinates.front() + vector_from_direction_per_rotation_degrees(
				input_direction,
				rotation_degrees
			)
		)
	return input_tile_coordinates


func _on_push_item(item: Item, sender: Building):
	# the push signal is emitted and has multiple consumers, thus only the first does actually
	# consume the item and other should ignore it.
	if item == null:
		return

	if is_occupied:
		return

	if reserved_by != null and sender != reserved_by:
		return
	
	item_received.emit(item, self)


func get_output_items() -> Array[Item]:
	return [_held_item]


func _on_item_received(item: Item, building: Building) -> void:
	if item == _held_item:
		item_transport_confirmed.emit(_held_item, building)
		_held_item = null


func _on_item_transport_confirm(item: Item, building: Building) -> void:
	if building == self:
		_held_item = item
		item.reparent(building)
		_held_item.move_to(self.global_position, 1.0)
		has_item.emit(item, self)


## A method that is executed by the building manager after the building was sucessfully built.[br]
## It might contain arbitrary code and it is supposed to be implemented by child classes.
func _post_on_building_built_callback(_other_building: Building) -> void:
	pass


## Checks whether this building is the input of some other building.
func is_input_of(other_building: ConveyorBasedBuilding):
	return other_building.tile_coordinates.front() in get_output_tile_coordinates() \
		and tile_coordinates.front() in other_building.get_input_tile_coordinates()


## Checks whether this building is the output of some other building.
func is_output_of(other_building: ConveyorBasedBuilding):
	return other_building.tile_coordinates.front() in get_input_tile_coordinates() \
		and tile_coordinates.front() in other_building.get_output_tile_coordinates()


## Connects the other building as an output of this building.
func connect_as_output(other_building: ConveyorBasedBuilding):
	other_building.input_buildings.append(self)
	output_buildings.append(other_building)
	
	push_item.connect(other_building._on_push_item, ConnectFlags.CONNECT_PERSIST)
	other_building.item_received.connect(_on_item_received, ConnectFlags.CONNECT_PERSIST)
	item_transport_confirmed.connect(
		other_building._on_item_transport_confirm,
		ConnectFlags.CONNECT_PERSIST
	)

# TODO: the initialization needs a separate method that is going to be called on load

func _on_building_built(other_building: Building) -> void:
	if other_building == self:
		return

	# Check if other building is not too distant to connect
	if tile_coordinates.front().distance_squared_to(other_building.tile_coordinates.front()) != 1:
		return

	# it is possible to connect only to other conveyor based buildings
	# After this point we know if the other building is 1x1
	if not other_building is ConveyorBasedBuilding:
		return

	if is_input_of(other_building):
		connect_as_output(other_building)

	if is_output_of(other_building):
		other_building.connect_as_output(self)

	_post_on_building_built_callback(other_building)
