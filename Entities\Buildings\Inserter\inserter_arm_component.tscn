[gd_scene load_steps=4 format=3 uid="uid://t3qyyt8sh0yk"]

[ext_resource type="Script" uid="uid://bsw6vth5jdfn1" path="res://Entities/Buildings/Inserter/inserter_arm.gd" id="1_ejy1b"]
[ext_resource type="Texture2D" uid="uid://cblumu145pr6m" path="res://assets/Sprites/32x32/InserterArm.png" id="2_nxs7g"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_sbkkv"]

[node name="InserterArmComponent" type="Node2D"]
script = ExtResource("1_ejy1b")

[node name="Inserter Arm" type="Node2D" parent="."]
position = Vector2(16, 0)

[node name="Attach Point" type="Area2D" parent="Inserter Arm"]
position = Vector2(16, 0)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Inserter Arm/Attach Point"]
shape = SubResource("RectangleShape2D_sbkkv")

[node name="Sprite2D" type="Sprite2D" parent="Inserter Arm"]
z_index = 6
rotation = 1.5708
texture = ExtResource("2_nxs7g")

[connection signal="body_entered" from="Inserter Arm/Attach Point" to="Inserter Arm" method="_on_attach_point_body_entered"]
