extends Node

const MAX_DEGREES: int = 360
@onready var ANIMATION_SYNC = load("res://Entities/Buildings/building base/animation_sync.tscn")

var selected_building_stats: BuildingStats = null
# GDScript does not have support for sets, so this is some way of simulating it
var occupied_tiles: Dictionary[Vector2i, Node2D]
# TODO: find better name
var sync: Node2D


func _ready() -> void:
	sync = ANIMATION_SYNC.instantiate()
	sync.hide()
	add_child(sync)
	sync.position = Vector2.ZERO
	sync.get_child(0).play("North")


var building_rotation: int = 0:
	set(value):
		building_rotation = value
		if building_rotation < 0:
			building_rotation += MAX_DEGREES
		building_rotation %= MAX_DEGREES
	get:
		return building_rotation


func set_tiles_as_occupied(building: Building) -> void:
	for tile_coordinate in building.tile_coordinates:
		BuildingModeManager.occupied_tiles[tile_coordinate] = building


func is_building_site_occupied(tilemap_position: Vector2i) -> bool:
	var building_dimensions: Vector2i = selected_building_stats.dimensions
	for x_offset in range(building_dimensions.x):
		for y_offset in range(building_dimensions.y):
			if (tilemap_position + Vector2i(x_offset, y_offset)) in occupied_tiles:
				return true
	return false


func reset() -> void:
	occupied_tiles.clear()


func _sync_animation(instance: Node2D, loading: bool) -> void:
	var animations: AnimatedSprite2D = instance.get_node("AnimatedSprite2D")

	if not animations.animation:
		return

	animations.play(
		"East" if instance.stats.is_rotateable else "North"
	)
	var sync_node = sync.get_child(0)
	animations.set_frame_and_progress(sync_node.frame, sync_node.frame_progress)
	#if not loading:
		#if BuildingModeManager.selected_building_stats.animation_frames:
			#animations.play(
				#"East" if BuildingModeManager.selected_building_stats.is_rotateable else "North"
			#)
			#var sync_node = BuildingModeManager.sync.get_child(0)
			#animations.set_frame_and_progress(sync_node.frame, sync_node.frame_progress)
	#
	#if loading:
		#animations.play(
			#"East" if instance.stats.is_rotateable else "North"
		#)
		#var sync_node = BuildingModeManager.sync.get_child(0)
		#animations.set_frame_and_progress(sync_node.frame, sync_node.frame_progress)
