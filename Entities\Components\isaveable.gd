extends RefCounted
class_name ISaveable

## Interface for components that can save and load their state
## Components implementing this interface should override these methods

## Save the component's state to a dictionary
## Returns: Dictionary containing all necessary data to restore the component's state
func save_data() -> Dictionary:
	push_error("save_data() must be implemented by the component")
	return {}

## Load the component's state from a dictionary
## Parameters: data - Dictionary containing the component's saved state
func load_data(data: Dictionary) -> void:
	push_error("load_data() must be implemented by the component")

## Get a unique identifier for this component type
## This is used to identify which component the save data belongs to
func get_save_id() -> String:
	push_error("get_save_id() must be implemented by the component")
	return ""
