extends Panel


@onready var new_game_option: VBoxContainer = $NewGameOption
@onready var continue_game_option: Control = $ContinueGameOption

func _ready() -> void:
	update_visual_state()


func update_visual_state():
	if SaveManager.has_global_data(name):
		continue_game_option.visible = true
		new_game_option.visible = false
	else:
		continue_game_option.visible = false
		new_game_option.visible = true


func _on_continue_button_pressed() -> void:
	SaveManager.selected_global_name = name
	SceneManager.to_space_map()
	
	
func _on_new_game_button_pressed() -> void:
	print("New game initialized")
	SaveManager.selected_global_name = name
	SceneManager.to_space_map()


func _on_delete_button_pressed() -> void:
	SaveManager.delete_global_data(name)
	update_visual_state()
