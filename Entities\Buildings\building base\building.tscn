[gd_scene load_steps=2 format=3 uid="uid://8ryqok64fofm"]

[ext_resource type="Script" uid="uid://bu31nqv1tyn4c" path="res://Entities/Buildings/building base/building.gd" id="1_ljip3"]

[node name="Building" type="Node2D"]
script = ExtResource("1_ljip3")

[node name="Physics Body" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Physics Body"]

[node name="Sprite2D" type="Sprite2D" parent="."]
z_index = 6

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
z_index = 6

[node name="Timer" type="Timer" parent="."]
one_shot = true

[node name="Components" type="Node2D" parent="."]

[connection signal="ready" from="." to="." method="_on_ready"]
[connection signal="script_changed" from="." to="." method="_on_script_changed"]
[connection signal="stats_changed" from="." to="." method="_on_stats_changed"]
