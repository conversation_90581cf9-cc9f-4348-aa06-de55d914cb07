class_name InserterArmComponent
extends Component

## Wrapper around the input and output position pair.
class ArmPositions:
	var input: Vector2i
	var output: Vector2i

	func _init(tile_coordinates: Vector2i, degrees: float) -> void:
		input = tile_coordinates
		output = tile_coordinates
		if is_equal_approx(degrees, 0.0):
			input += Vector2i(1, 0)
			output += Vector2i(-1, 0)
		elif is_equal_approx(degrees, 90.0):
			input += Vector2i(0, 1)
			output += Vector2i(0, -1)
		elif is_equal_approx(degrees, 180.0):
			input += Vector2i(-1, 0)
			output +=  Vector2i(1, 0)
		elif is_equal_approx(degrees, 270.0):
			input += Vector2i(0, -1)
			output += Vector2i(0, 1)

## Emitted when the arm finished its moving.
signal move_finished

@onready var arm_sprite := $"Inserter Arm/Sprite2D"
@onready var attach_point := $"Inserter Arm/Attach Point"

@export var arm_transition_time := 1.0

## Pair of positions of the inserter arm.
var arm_positions: ArmPositions = null
## Flag that tells whether the arm is currently in the input position.[br]
## After initialization, the arm always is in the input position. 
var is_in_input_position = true
## Flag that tells whether the arm is currently moving
var is_moving = false


## Attaches a node to the arm
func attach(node: Node2D) -> void:
	if not node:
		return
	node.reparent(self)


## Initializes the arm positions.
func initialize_arm(tile_coordinates: Vector2i, degrees: float) -> void:
	arm_positions = ArmPositions.new(tile_coordinates, degrees)


func _get_target_rotation_degrees() -> float:
	return get_parent().rotation_degrees + 180.0


func switch_sides():
	var tween: Tween = create_tween()
	tween.set_loops(1)
	tween.finished.connect(_on_tween_finished)
	
	is_moving = true
	
	tween.tween_property(
		get_parent(),
		"rotation_degrees",
		_get_target_rotation_degrees(),
		arm_transition_time
	)


func _on_tween_finished():
	is_in_input_position = not is_in_input_position
	move_finished.emit()
	is_moving = false
