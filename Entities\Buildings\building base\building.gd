class_name Building
extends Node2D

## Emitted when the stats of a building did change.
signal stats_changed

@export_category("Prefab Data")
## The reference to the resource that is setting the stats of current building.
@export var stats: BuildingStats:
	set(new_stats):
		stats = new_stats
		stats_changed.emit()
	get:
		return stats

## The sprite of the building that shall be used mainly for icons in the UI.
@onready var sprite: Sprite2D = $Sprite2D
## An animation of the building that is showing that the building is in a process.
@onready var animation: AnimatedSprite2D = $AnimatedSprite2D
## The shape of the building collision
@onready var collision_shape: CollisionShape2D = $"Physics Body/CollisionShape2D"

@export_category("Runtime Data")
@export var tile_coordinates: Array[Vector2i]
@export var components_node: Node2D


## Calculates and populates the tile coordinates occupied by this building.
func calculate_tile_coordinates_from_origin(origin: Vector2i) -> void:
	for x_offset in range(stats.dimensions.x):
		for y_offset in range(stats.dimensions.y):
			tile_coordinates.append(origin + Vector2i(x_offset, y_offset))


## Loads the building to the scene.[br]
## It should be used only by the save manager.
func load_to_scene() -> void:
	# bind current stats of the building. Otherwise the sprites do not have correct
	# values.
	bind_stats()

	# Update the stats of all tiems held by this building.
	if self is ItemHandlingBuilding:
		for item: Item in (self as ItemHandlingBuilding).get_output_items():
			if item != null:
				item.reload_stats()

	# Update the state of building mode manager.
	BuildingModeManager.set_tiles_as_occupied(self)
	BuildingModeManager._sync_animation(self, true)


## Binds currently assigned stats to child nodes.[br]
## If we bind new stats we have to manually change them.
func bind_stats() -> void:
	sprite.texture = stats.texture

	animation.sprite_frames = stats.animation_frames
	if animation.sprite_frames != null:
		sprite.hide()

	collision_shape.shape = stats.collision_shape


func initialize_components() -> void:
	components_node = $Components
	# initialize all components
	for component in stats.components:
		var new_node: Node2D
		if component is Script:
			new_node = Node2D.new()
			new_node.script = component
			new_node.name = component.get_global_name()
		elif component is PackedScene:
			new_node = component.instantiate()
		else:
			push_error("Unknown type of resource encountered in the list of components.")
			continue
		components_node.add_child(new_node)


## Method that is called by the builder at the moment of building a Building.[br]
## It is supposed to be implemented by child classes.
func on_build() -> void:
	pass


# TODO: this might not be needed
func connect_to_others() -> void:
	pass


# This has to be defined to connect it properly to some signals. At runtime the override defined
# by some child shall be used.
func _on_building_built(_building: Building) -> void:
	pass


func _on_ready() -> void:
	if stats == null:
		return
	bind_stats()
