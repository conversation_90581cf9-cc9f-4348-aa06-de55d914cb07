[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=8 format=3 uid="uid://blcu6tjgdxvah"]

[ext_resource type="SpriteFrames" uid="uid://bry1m3sb4n7xr" path="res://Assets/Sprites/Animations/32x32/Saw.tres" id="1_sxtut"]
[ext_resource type="Script" uid="uid://b8bs15us03x8b" path="res://Entities/Buildings/_simple_processing/simple_processing_building.gd" id="2_crhkx"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="3_xv212"]
[ext_resource type="Script" uid="uid://dfrsc76sgl6kk" path="res://Entities/Components/Power/power_consumer_component.gd" id="4_ngot7"]
[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="5_ry5tw"]
[ext_resource type="Texture2D" uid="uid://tumn8wjiprhh" path="res://Assets/Sprites/32x32/SpriteSheets/Saw_sprite_sheet_anim.png" id="6_qi8to"]

[sub_resource type="AtlasTexture" id="AtlasTexture_golqk"]
atlas = ExtResource("6_qi8to")
region = Rect2(0, 0, 32, 32)

[resource]
resource_name = "Saw"
script = ExtResource("5_ry5tw")
input_directions = 2
output_directions = 1
transport_speed = 1.0
translation_key = ""
dimensions = Vector2i(1, 1)
is_rotateable = true
building_type = 10
components = Array[Resource]([ExtResource("4_ngot7"), ExtResource("3_xv212")])
cost = Dictionary[int, int]({})
texture = SubResource("AtlasTexture_golqk")
animation_frames = ExtResource("1_sxtut")
building_script = ExtResource("2_crhkx")
unlocked_by_default = false
menu_order_priority = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"
