[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=8 format=3 uid="uid://x0fd41s4t43f"]

[ext_resource type="SpriteFrames" uid="uid://d02wc4ossn0gs" path="res://Assets/Sprites/Animations/32x32/Furnace.tres" id="1_gke8d"]
[ext_resource type="Script" uid="uid://cytdn6tym1msi" path="res://Entities/Buildings/smeltery/smeltery.gd" id="2_ot651"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="3_gke8d"]
[ext_resource type="Script" uid="uid://dfrsc76sgl6kk" path="res://Entities/Components/Power/power_consumer_component.gd" id="4_ot651"]
[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="5_bmskg"]
[ext_resource type="Texture2D" uid="uid://tmaenl3xl0gu" path="res://Assets/Sprites/32x32/SpriteSheets/Furnace_sprite_sheet_anim.png" id="6_k3qa8"]

[sub_resource type="AtlasTexture" id="AtlasTexture_qt0ab"]
atlas = ExtResource("6_k3qa8")
region = Rect2(0, 0, 32, 32)

[resource]
resource_name = "Smelter"
script = ExtResource("5_bmskg")
input_directions = 2
output_directions = 1
transport_speed = 1.0
translation_key = ""
dimensions = Vector2i(1, 1)
is_rotateable = true
building_type = 8
components = Array[Resource]([ExtResource("3_gke8d"), ExtResource("4_ot651")])
cost = Dictionary[int, int]({})
texture = SubResource("AtlasTexture_qt0ab")
animation_frames = ExtResource("1_gke8d")
building_script = ExtResource("2_ot651")
unlocked_by_default = true
menu_order_priority = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"
