extends Resource
class_name UnlockManagerData

@export_category("Unlock Manager Data")
## Array of Unlocked buildings
@export var buildings_unlocked: Array[BuildingStats]
## Dictionary of Unlocked simple building recipies (only one resource as input)
@export var unlocked_simple_recipes_catalog: Dictionary[BuildingType.Enum, Dictionary]
## Dictionary of Unlocked complex building recipies (multiple resources as input)
@export var unlocked_complex_recipes_catalog: Dictionary[BuildingType.Enum, Array]
