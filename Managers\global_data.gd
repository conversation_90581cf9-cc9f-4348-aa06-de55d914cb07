extends Resource
class_name GlobalData


@export var unlockManagerData: UnlockManagerData


func get_unlock_manager_data() -> UnlockManagerData:
	if not unlockManagerData:
		unlockManagerData = UnlockManagerData.new()
	return unlockManagerData


@export var unlockableNodeData: UnlockableNodeData


func get_unlockable_node_data() -> UnlockableNodeData:
	if not unlockableNodeData:
		unlockableNodeData = UnlockableNodeData.new()
	return unlockableNodeData
