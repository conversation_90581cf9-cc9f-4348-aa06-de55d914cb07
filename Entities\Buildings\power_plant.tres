[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=9 format=3 uid="uid://cg0uhaqc3hn0r"]

[ext_resource type="SpriteFrames" uid="uid://btjmd6fwiovoc" path="res://Assets/Sprites/Animations/64x64/Powerplant.tres" id="1_bxnu3"]
[ext_resource type="Script" uid="uid://ckpkod1ig3bqo" path="res://Entities/power_plant.gd" id="2_2ut2i"]
[ext_resource type="Script" uid="uid://cxc5br52a2krl" path="res://Entities/Components/Power/power_provider_component.gd" id="4_0vi8k"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="5_bxnu3"]
[ext_resource type="Script" uid="uid://h5swltwpuw8s" path="res://Entities/Components/Power/power_buffer_component.gd" id="5_k4g2s"]
[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="6_g6r32"]
[ext_resource type="Texture2D" uid="uid://dwrlemvpyaro" path="res://Assets/Sprites/32x32/SpriteSheets/PowerPlant_sprite_sheet_anim.png" id="7_60p33"]

[sub_resource type="AtlasTexture" id="AtlasTexture_8qjvx"]
atlas = ExtResource("7_60p33")
region = Rect2(0, 0, 64, 64)

[resource]
resource_name = "Power plant"
script = ExtResource("6_g6r32")
input_directions = 15
output_directions = 0
transport_speed = 1.0
translation_key = ""
dimensions = Vector2i(2, 2)
is_rotateable = false
building_type = 5
components = Array[Resource]([ExtResource("4_0vi8k"), ExtResource("5_k4g2s"), ExtResource("5_bxnu3")])
cost = Dictionary[int, int]({})
texture = SubResource("AtlasTexture_8qjvx")
animation_frames = ExtResource("1_bxnu3")
building_script = ExtResource("2_2ut2i")
unlocked_by_default = true
menu_order_priority = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"
