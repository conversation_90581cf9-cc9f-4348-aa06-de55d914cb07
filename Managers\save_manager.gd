extends Node

const SAVE_ROOT := "res://saves"

## Signal to notify when global data is changed
signal global_data_changed(new_global_data: GlobalData)

## Signal to notify when planet data is changed
signal planet_data_changed(new_planet_data: PlanetData)

var _global_data: GlobalData
var _planet_data: PlanetData

## Internal name of save
var selected_global_name: String:
	set(value):
		if selected_global_name != value:
			# Invalidate scene data
			selected_planet_name = ""

			selected_global_name = value
			
			if not selected_global_name.is_empty():
				load_global_data()
			else:
				# Invalidate global data
				_global_data = null

## Internal name of planet
var selected_planet_name: String:
	set(value):
		if selected_planet_name != value:
			# save old data
			if not selected_planet_name.is_empty():
				save_planet_data()
				save_global_data()

			selected_planet_name = value

			if not selected_planet_name.is_empty():
				load_planet_data()
			else:
				# Invalidate scene data
				_planet_data = null

var GLOBAL_ROOT: String:
	get:
		if selected_global_name.is_empty():
			return ""
		return SAVE_ROOT + "/" + selected_global_name + "/"

var GLOBAL_FILE: String:
	get:
		if GLOBAL_ROOT.is_empty():
			return ""
		return GLOBAL_ROOT + "global_data.tres"

var PLANET_ROOT: String:
	get:
		if selected_planet_name.is_empty():
			return ""
		return GLOBAL_ROOT + "/" + selected_planet_name + "/"

var PLANET_FILE: String:
	get:
		if PLANET_ROOT.is_empty():
			return ""
		return PLANET_ROOT + "planet_data.tres"

## Check if global data exists
func has_global_data(global_name: String) -> bool:
	return ResourceLoader.exists(SAVE_ROOT + "/" + global_name + "/" + "global_data.tres")

## Load global data
func load_global_data() -> void:
	print("Loading global data for:", selected_global_name)
	if has_global_data(selected_global_name):
		_global_data = ResourceLoader.load(GLOBAL_FILE)
		print("Global data loaded successfully.")
	else:
		print("No existing global data found. Creating new data.")
		_global_data = GlobalData.new()
	save_global_data()
	global_data_changed.emit(_global_data)

## Save global data
func save_global_data() -> void:
	DirAccess.make_dir_recursive_absolute(GLOBAL_ROOT)
	var state := ResourceSaver.save(_global_data, GLOBAL_FILE)
	if state != OK:
		push_error("Saving global data failed with state: ", state)
	else:
		print("Global data saved successfully.")

## Returns global data
func get_global_data() -> GlobalData:
	return _global_data

## Delete global data
## NOTE: This also deletes all planet data !
func delete_global_data(global_name: String) -> void:
	if selected_global_name == global_name:
		selected_global_name = ""

	if has_global_data(global_name):
		delete_dir_recursive(SAVE_ROOT + "/" + global_name)
	else:
		push_error("Save does not exist so can't be deleted")

func delete_dir_recursive(path: String) -> void:
	var dir := DirAccess.open(path)
	if not dir:
		push_error("Could not open directory: " + path)
		return

	dir.list_dir_begin()
	var file_name := dir.get_next()
	while file_name != "":
		if file_name in [".", ".."]:
			file_name = dir.get_next()
			continue

		var file_path := path + "/" + file_name
		if dir.current_is_dir():
			delete_dir_recursive(file_path)
		else:
			var remove_file_state := DirAccess.remove_absolute(file_path)
			if remove_file_state != OK:
				push_error("Failed to delete file: " + file_path)
		file_name = dir.get_next()
	dir.list_dir_end()
	# Now delete the (now-empty) folder itself
	var remove_dir_state := DirAccess.remove_absolute(path)
	if remove_dir_state != OK:
		push_error("Failed to delete directory: " + path)

## Check if planet data exists
func has_planet_data(planet_name: String) -> bool:
	return ResourceLoader.exists(GLOBAL_ROOT + "/" + planet_name + "/" + "planet_data.tres")

## Load planet data
func load_planet_data() -> void:
	print("Loading planet data for:", selected_planet_name)
	if has_planet_data(selected_planet_name):
		_planet_data = ResourceLoader.load(PLANET_FILE)
		print("Planet data loaded successfully.")
	else:
		push_warning("No existing planet data found. Creating new data.")
		_planet_data = PlanetData.new()
	save_planet_data()
	planet_data_changed.emit(_planet_data)

## Save planet data
## No need to call this manually as it is called automatically when planet data is changed
func save_planet_data() -> void:
	DirAccess.make_dir_recursive_absolute(PLANET_ROOT)
	var state := ResourceSaver.save(_planet_data, PLANET_FILE)
	if state != OK:
		push_error("Saving planet data failed with state: ", state)
	else:
		print("Planet data saved successfully.")

## Returns planet data
func get_planet_data() -> PlanetData:
	return _planet_data


## Generate a unique save name for a component
func get_component_save_name(node: Node) -> String:
	var component_name: String
	var script := node.get_script() as Script
	if script:
		# Try to get the class name from the script, fallback to file name if not available
		if script.has_method("get_class"):
			component_name = script.get_class()
		else:
			component_name = String(script.resource_path).get_file().get_basename()
	else:
		component_name = node.get_class()

	var node_path := str(node.get_path())
	return "%s_%s" % [component_name, node_path.hash()]

## Just reset selected save data
func exit_save() -> void:
	selected_global_name = ""

## Just reset selected planet data
func exit_planet() -> void:
	selected_planet_name = ""
