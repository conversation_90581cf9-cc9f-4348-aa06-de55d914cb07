[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=6 format=3 uid="uid://b41uoarpo0cmu"]

[ext_resource type="SpriteFrames" uid="uid://xitsonu8ruad" path="res://Assets/Sprites/Animations/32x32/Miner.tres" id="1_vn70r"]
[ext_resource type="Script" uid="uid://cfikvvvwxi0hy" path="res://Entities/Buildings/mine/mine.gd" id="2_15huj"]
[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="3_bqbb1"]
[ext_resource type="Texture2D" uid="uid://ctiwmxcyn186a" path="res://Assets/Sprites/32x32/SpriteSheets/Miner_sprite_sheet_anim.png" id="4_pfdbl"]

[sub_resource type="AtlasTexture" id="AtlasTexture_ykjd5"]
atlas = ExtResource("4_pfdbl")
region = Rect2(0, 0, 32, 32)

[resource]
resource_name = "Mine"
script = ExtResource("3_bqbb1")
input_directions = 0
output_directions = 15
transport_speed = 0.0
translation_key = "MINE"
dimensions = Vector2i(1, 1)
is_rotateable = false
building_type = 2
components = Array[Resource]([])
cost = Dictionary[int, int]({})
texture = SubResource("AtlasTexture_ykjd5")
animation_frames = ExtResource("1_vn70r")
building_script = ExtResource("2_15huj")
unlocked_by_default = true
menu_order_priority = -2
metadata/_custom_type_script = "uid://qrdti4s4su0t"
