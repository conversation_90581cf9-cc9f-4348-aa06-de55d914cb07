extends Resource
class_name BuildingSaveData

## Position of the building in tile coordinates
@export var position: Vector2i

## Resource path to the BuildingStats resource used by this building
@export var building_stats_path: String

## Rotation of the building (0, 90, 180, 270 degrees)
@export var rotation: int = 0

## Custom component data stored as key-value pairs
## Key: component name (String), Value: component save data (Dictionary)
@export var component_data: Dictionary = {}

## Tile coordinates occupied by this building
@export var tile_coordinates: Array[Vector2i] = []

## Initialize with basic building information
func _init(pos: Vector2i = Vector2i.ZERO, stats_path: String = "", rot: int = 0):
	position = pos
	building_stats_path = stats_path
	rotation = rot

## Set component data for a specific component
func set_component_data(component_name: String, data: Dictionary) -> void:
	component_data[component_name] = data

## Get component data for a specific component
func get_component_data(component_name: String) -> Dictionary:
	return component_data.get(component_name, {})

## Check if component data exists for a specific component
func has_component_data(component_name: String) -> bool:
	return component_data.has(component_name)

## Clear all component data
func clear_component_data() -> void:
	component_data.clear()
