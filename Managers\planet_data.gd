extends Resource
class_name PlanetData

## Array of saved building data for this planet
@export var buildings: Array[BuildingSaveData] = []

## Add a building to the saved data
func add_building(building_data: BuildingSaveData) -> void:
	buildings.append(building_data)

## Remove a building from the saved data by position
func remove_building_at_position(position: Vector2i) -> bool:
	for i in range(buildings.size()):
		if buildings[i].position == position:
			buildings.remove_at(i)
			return true
	return false

## Get building data at a specific position
func get_building_at_position(position: Vector2i) -> BuildingSaveData:
	for building_data in buildings:
		if building_data.position == position:
			return building_data
	return null

## Clear all building data
func clear_buildings() -> void:
	buildings.clear()
