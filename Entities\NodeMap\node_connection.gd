class_name NodeConnection
extends Node2D

@export var connection_duration  := 0.5
@export var expansion_duration   := 0.1
@export var contraction_duration := 0.2

@export var primary_line: Line2D
@export var secondary_line: Line2D

var from_pos: Vector2
var to_pos: Vector2

var _is_active := false

var is_active: bool:
	get():
		return _is_active

signal connection_activated

func initialize(start_node: UnlockableNode, end_node: UnlockableNode) -> void:
	from_pos = start_node.global_position - self.global_position
	to_pos = end_node.global_position - self.global_position
	
	primary_line.add_point(from_pos)
	primary_line.add_point(to_pos)

	secondary_line.add_point(from_pos)
	secondary_line.add_point(from_pos)
	
	start_node.outgoing_links.append(self)
	end_node.incoming_links.append(self)
	
	# Load state from save
	var data := SaveManager.get_global_data().get_unlockable_node_data()
	var key := SaveManager.get_component_save_name(self)
	# NOTE: this variable is used bc activate() has guard against multiple activations
	var active: bool= data.link_states.get_or_add(key, is_active)
	
	# Connect signal to save state
	connection_activated.connect(
		func() -> void:
			data.set(key, true)
	)
	
	# Activate if needed
	if active:
		set_active()


## Plays fancy animation and activates node
func activate() -> void:
	if _is_active:
		return
	
	var tween := get_tree().create_tween()

	# Animate line length
	tween.tween_method(
		func(tween_value: Vector2) -> void: secondary_line.set_point_position(1, tween_value),
			from_pos, to_pos, connection_duration
	).set_trans(Tween.TRANS_CUBIC)

	# Animate width expansion (after line length animation finishes)
	var normal_width   := secondary_line.width
	var expanded_width := normal_width * 4
	tween.tween_method(
		func(tween_value: float) -> void: secondary_line.width = tween_value,
			normal_width, expanded_width, expansion_duration
	).set_trans(Tween.TRANS_BOUNCE)

	# Animate width contraction (after expansion)
	tween.tween_method(
		func(tween_value: float) -> void: secondary_line.width = tween_value,
			expanded_width, normal_width, connection_duration
	).set_trans(Tween.TRANS_CUBIC)

	set_active()


## Just activates connection (No fancy animation)
func set_active() -> void:
	if _is_active:
		return
	
	_is_active = true
	secondary_line.set_point_position(1, to_pos)
	connection_activated.emit()
