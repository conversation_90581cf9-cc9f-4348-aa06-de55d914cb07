class_name In<PERSON><PERSON>
extends ItemHandlingBuilding

# NOTE: Currently, the inserter is blocking, as it reserves its space in the output
# building, it makes sense for crafting and production buildings, but it does not behave
# so good with conveyor belts. We have to come up with some more clever approach.

@onready var arm_component: InserterArmComponent = $Components/InserterArmComponent

var _held_item: Item = null

func get_output_items() -> Array[Item]:
	return [_held_item]


func _ready() -> void:
	arm_component.move_finished.connect(_on_arm_move_finished)


func _on_item_received(item: Item, building: Building) -> void:
	if item == _held_item:
		item_transport_confirmed.emit(_held_item, building)
		_held_item = null
		if item.movement_finished.is_connected(arm_component.switch_sides):
			item.movement_finished.disconnect(arm_component.switch_sides)
		arm_component.switch_sides()


func _is_input(building: ItemHandlingBuilding) -> bool:
	if building == null:
		return false
	if arm_component.arm_positions.input not in building.tile_coordinates:
		return false
	if building is not ItemHandlingBuilding:
		return false
	return true


func _is_output(building: ItemHandlingBuilding) -> bool:
	if building == null:
		return false
	if arm_component.arm_positions.output not in building.tile_coordinates:
		return false
	if building is not ItemHandlingBuilding:
		return false
	return true


func _connect_as_input(building: ItemHandlingBuilding) -> void:
	building.has_item.connect(_on_has_item)
	item_received.connect(building._on_item_received)
	building.item_transport_confirmed.connect(_on_item_transport_confirm)
	input_buildings.append(building)
	building.output_buildings.append(self)


func _connect_as_output(building: ItemHandlingBuilding) -> void:
	push_item.connect(building._on_push_item)
	building.item_received.connect(_on_item_received)
	item_transport_confirmed.connect(building._on_item_transport_confirm)
	output_buildings.append(building)
	building.input_buildings.append(self)


# Due to the fact, that other buildings do not have to know that an inserter is attached, and
# also because I don't want to further complicate the conveyor belt building method the inserter
# is able to connect to other buildings on build.
func on_build() -> void:
	arm_component.initialize_arm(tile_coordinates.front(), rotation_degrees)
	
	var input_building: Building = BuildingModeManager.occupied_tiles.get(
		arm_component.arm_positions.input,
		null
	)
	if _is_input(input_building):
		_connect_as_input(input_building)
	
	var output_building: Building = BuildingModeManager.occupied_tiles.get(
		arm_component.arm_positions.output,
		null
	)
	if _is_output(output_building):
		_connect_as_output(output_building)


func _on_building_built(other_building: Building) -> void:
	if _is_input(other_building):
		_connect_as_input(other_building)
	elif _is_output(other_building):
		_connect_as_output(other_building)


func _is_item_pushable(item: Item) -> bool:
	if arm_component.is_in_input_position:
		return false
	if not _held_item:
		return false
	if arm_component.is_moving:
		return false
	return true


func _on_has_item(item: Item, building: Building) -> void:
	if not arm_component.is_in_input_position:
		return

	if arm_component.is_moving:
		return

	if _held_item != null:
		return

	if output_buildings.is_empty():
		return

	if output_buildings.front().is_occupied:
		return

	# Reserve our space in the output building
	output_buildings.front().reserve(self)
	
	item_received.emit(item, self)
	return


func _on_item_transport_confirm(item: Item, building: Building) -> void:
	if building == self:
		_held_item = item
		item.reparent(arm_component.attach_point)
		if item.is_moving():
			item.movement_finished.connect(arm_component.switch_sides)
		else:
			arm_component.switch_sides()
	else:
		if output_buildings.front().reserved_by == self:
			output_buildings.front().reserved_by == null


func _on_arm_move_finished() -> void:
	push_item.emit(_held_item, self)
