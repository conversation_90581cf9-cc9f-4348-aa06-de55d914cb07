[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=5 format=3 uid="uid://djfskeupp2xap"]

[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="1_eo5km"]
[ext_resource type="Script" uid="uid://cbjv6814ay2id" path="res://Entities/Buildings/Inserter/inserter.gd" id="1_qghj2"]
[ext_resource type="PackedScene" uid="uid://t3qyyt8sh0yk" path="res://Entities/Buildings/Inserter/inserter_arm_component.tscn" id="1_sy8o3"]
[ext_resource type="Texture2D" uid="uid://brro0svjm78l2" path="res://assets/Sprites/32x32/InserterBase.png" id="4_7gx43"]

[resource]
resource_name = "Inserter"
script = ExtResource("1_eo5km")
input_directions = 3
output_directions = 3
transport_speed = 0.0
translation_key = "INSERTER"
dimensions = Vector2i(1, 1)
is_rotateable = true
building_type = 0
components = Array[Resource]([ExtResource("1_sy8o3")])
cost = Dictionary[int, int]({})
texture = ExtResource("4_7gx43")
building_script = ExtResource("1_qghj2")
unlocked_by_default = true
menu_order_priority = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"
