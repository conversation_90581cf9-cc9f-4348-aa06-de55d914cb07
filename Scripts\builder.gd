extends Node2D

@onready var tilemap: TileMapLayer = $"../Tiles/Ground"
@onready var level_manager: LevelManager = get_parent() as LevelManager

const BUILDING_PREFAB: PackedScene = preload("res://Entities/Buildings/building base/building.tscn")
@onready var buildings_node: Node2D = $"../Buildings"

@warning_ignore("unused_signal")
signal building_built(where: Vector2i, dimensions: Vector2i)
signal building_blocked(what: String, why: String)
signal building_mode_cancelled


var is_dragging: bool = false
var start_building_tile: Vector2i = Vector2i.ZERO
var last_mouse_tile: Vector2i = Vector2i.ZERO
var new_mouse_tile: Vector2i = Vector2i.ZERO
var direction_vector: Vector2i = Vector2i.ZERO


func _building_scaling(building_prefab: Node) -> void:
	var tile_size = tilemap.tile_set.tile_size
	var collider: CollisionShape2D = building_prefab.find_child("CollisionShape2D", true, false)

	# Ensure the shape is initialized issue when instancing even if shape is assigned in inspector
	if collider.shape == null:
		collider.shape = RectangleShape2D.new()

	# Check type and set size
	if collider.shape is RectangleShape2D:
		var shape: RectangleShape2D = collider.shape
		shape.size = BuildingModeManager.selected_building_stats.dimensions * tile_size
		
	else:
		print("⚠️ Shape is not RectangleShape2D: ", collider.shape)


func _cancel_building():
	is_dragging = false
	StateManager.state = StateManager.States.STATE_PLAY
	BuildingModeManager.building_rotation = 0
	building_mode_cancelled.emit()


func _build_building_at_position(world_position: Vector2i):
	var tilemap_position: Vector2i = tilemap.local_to_map(world_position)
	
	if tilemap.get_cell_tile_data(tilemap_position) == null:
		LogManager.log("Trying to build on invalid tile on position %s" % tilemap_position,LogManager.LogLevel.WARNING, self)
		return
	
	if BuildingModeManager.is_building_site_occupied(tilemap_position):
		building_blocked.emit(
			BuildingModeManager.selected_building_stats.resource_name,
			'Something is already built there!'
		)
		return

	var building_cost = BuildingModeManager.selected_building_stats.cost
	if not level_manager.inventory.sufficient_resources(building_cost):
		return
	level_manager.inventory.consume_resources(building_cost)

	# initialize the building instance. We have to manually initialize components that we can get
	# from a this building's stats. After that we require new call to the ready function (it should
	# not be called manually.
	var instance: Building = BUILDING_PREFAB.instantiate()
	instance.script = BuildingModeManager.selected_building_stats.building_script
	instance.stats = BuildingModeManager.selected_building_stats
	instance.initialize_components()
	instance.request_ready()

	# Each building has to be connected to the bus to know that some other building has been built.
	# This connection should persist when saved on the disk.
	BuildingSignalBus.building_built.connect(
		instance._on_building_built,
		 ConnectFlags.CONNECT_PERSIST
	)

	#Take into account building dimensions offset
	if BuildingModeManager.selected_building_stats.dimensions != Vector2i.ONE:
		var tile_size = tilemap.tile_set.tile_size / 4
		var offset = tile_size * BuildingModeManager.selected_building_stats.dimensions
		world_position += offset

	instance.position = world_position
	instance.calculate_tile_coordinates_from_origin(tilemap_position)
	BuildingModeManager.set_tiles_as_occupied(instance)

	# Finally, add the building a node to the building manager, at this moment, it should be
	# fully initialized.
	buildings_node.add_child(instance)
	instance.owner = buildings_node

	# Handling building rotation
	_sync_rotation(instance)
	# Handling animations and syncing them
	BuildingModeManager._sync_animation(instance, false)

	_building_scaling(instance)

	AudioManager.create_audio(SoundEffect.SOUND_EFFECT_TYPE.BUILDING_PLACED)

	instance.on_build()

	BuildingSignalBus.building_built.emit(instance)


func _sync_rotation(instance:Node2D) -> void:
	if not BuildingModeManager.selected_building_stats.is_rotateable:
		return
		
	#NOTE one needs rotation as an int other as float
	var _rotation: int = BuildingModeManager.building_rotation
	if BuildingModeManager.selected_building_stats.building_type == BuildingType.Enum.CONVEYOR_BELT:
		if start_building_tile != new_mouse_tile:
			_rotation = vector_to_degrees(direction_vector)
	
	BuildingModeManager.building_rotation = _rotation
	instance.rotation_degrees = float(_rotation)


func vector_to_degrees(direction: Vector2) -> int:
	match direction:
		Vector2(1, 0): return 0    # East
		Vector2(0, -1): return 270  # North
		Vector2(-1, 0): return 180 # West
		Vector2(0, 1): return 90  # South
		_:
			push_error("Invalid cardinal direction vector: %s" % direction)
			return 0


func _on_hud_caught_input(event: InputEvent) -> void:
	if StateManager.state != StateManager.States.STATE_BUILD:
		return
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_LEFT:
			var local_mouse_pos: Vector2 = to_local(get_global_mouse_position())

			if event.is_pressed():
				start_building_tile = tilemap.local_to_map(local_mouse_pos)
				last_mouse_tile = tilemap.local_to_map(local_mouse_pos)
				is_dragging = true

			if event.is_released():
				_build_building_at_position(tilemap.map_to_local(last_mouse_tile))
				is_dragging = false

		if event.button_index == MOUSE_BUTTON_RIGHT:
			_cancel_building()


func _process(_delta):
	if not is_dragging:
		return

	var local_mouse_pos = to_local(get_global_mouse_position())
	new_mouse_tile = tilemap.local_to_map(local_mouse_pos)
	
	if new_mouse_tile == last_mouse_tile:
		return
	
	direction_vector = new_mouse_tile-last_mouse_tile
	
	if new_mouse_tile in _get_cardinal_directions(last_mouse_tile):
		_build_building_at_position(tilemap.map_to_local(last_mouse_tile))
		last_mouse_tile = new_mouse_tile
	else:
		var path=_get_simple_path(last_mouse_tile,new_mouse_tile)
		
		#path gets constructed to the last but one tile so 2 different 
		#paths could be connected by the default logic
		for tile in range(path.size()-1):
			last_mouse_tile = path[tile]
			new_mouse_tile = path[tile+1]
			direction_vector=new_mouse_tile-last_mouse_tile
			_build_building_at_position(tilemap.map_to_local(last_mouse_tile))
			last_mouse_tile = new_mouse_tile


func _get_simple_path(start: Vector2i, end: Vector2i) -> Array[Vector2i]:
	var path: Array[Vector2i] = []
	path.append(start)
	#get for x
	while start.x != end.x:
		start.x+= 1 if end.x>start.x else -1
		path.append(start)
	
	#get for y
	while start.y != end.y:
		start.y+= 1 if end.y>start.y else -1
		path.append(start)

	return path


const CARDINAL_VECTORS = [
	Vector2i(1,0),
	Vector2i(-1,0),
	Vector2i(0,1),
	Vector2i(0,-1)
]


func _get_cardinal_directions(tile_position : Vector2i) -> Array[Vector2i]:
	var tile_array : Array[Vector2i] = []
	for direction in CARDINAL_VECTORS:
		tile_array.append(tile_position + direction)
	return tile_array


func _unhandled_key_input(_event: InputEvent) -> void:
	if StateManager.state != StateManager.States.STATE_BUILD:
		return
	if Input.is_action_just_pressed("rotate_left"):
		BuildingModeManager.building_rotation -= 90
	if Input.is_action_just_pressed("rotate_right"):
		BuildingModeManager.building_rotation += 90
	if Input.is_key_pressed(KEY_DELETE):
		BuildingModeManager.occupied_tiles.clear()
		for node in buildings_node.get_children():
			node.queue_free()
		
