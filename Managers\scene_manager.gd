extends Node

const MAIN_MENU := preload("res://Scenes/Menus/MainMenu.tscn")
const GENERAL_SETTINGS := preload("res://Scenes/Menus/GeneralSettings.tscn")
const RESEARCH_MAP_MENU := preload("res://Scenes/Menus/ResearchMapMenu.tscn")
const SPACE_MAP_MENU := preload("res://Scenes/Menus/SpaceMapMenu.tscn")
const SAVE_SELECT := preload("res://Scenes/Menus/SaveSelect/SaveSelect.tscn")

func _play_button_click_sound() -> void:
	AudioManager.create_audio(SoundEffect.SOUND_EFFECT_TYPE.UI_BUTTON_CLICKED)


func to_main_menu() -> void:
	_play_button_click_sound()
	SaveManager.exit_save()
	get_tree().change_scene_to_packed(MAIN_MENU)


func to_settings() -> void:
	_play_button_click_sound()
	SaveManager.exit_save()
	get_tree().change_scene_to_packed(GENERAL_SETTINGS)


func to_save_menu() -> void:
	_play_button_click_sound()
	SaveManager.exit_save()
	get_tree().change_scene_to_packed(SAVE_SELECT)


func to_research_map() -> void:
	_play_button_click_sound()
	SaveManager.exit_planet()
	_try_load(RESEARCH_MAP_MENU)


func to_space_map() -> void:
	_play_button_click_sound()
	SaveManager.exit_planet()
	_try_load(SPACE_MAP_MENU)


## Load scene and try to load scene state
func _try_load(scene: PackedScene) -> void:
	SaveManager.selected_planet_name = scene.resource_path.get_file().get_basename()
	get_tree().change_scene_to_packed(scene)


func to_planet_surface(planet_scene: PackedScene) -> void:
	_play_button_click_sound()
	_try_load(planet_scene)
