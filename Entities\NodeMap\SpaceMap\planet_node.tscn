[gd_scene load_steps=12 format=3 uid="uid://bm2niqbcccmqu"]

[ext_resource type="Script" uid="uid://ceksvxnsqrnuy" path="res://Entities/NodeMap/SpaceMap/planet_node.gd" id="1_1odxm"]
[ext_resource type="Shader" uid="uid://3y3bf8j37a52" path="res://Shaders/flash.gdshader" id="2_fy6yj"]
[ext_resource type="SpriteFrames" uid="uid://qx7qkbtalpo0" path="res://Assets/Sprites/Animations/LowPolyPlanetAnimation.tres" id="3_fy6yj"]
[ext_resource type="Script" uid="uid://bkweay1fdq6a8" path="res://Scripts/run_animation_on_start.gd" id="5_dypwo"]
[ext_resource type="PackedScene" uid="uid://d3e3ovy5ey6ww" path="res://Scenes/Menus/SelectedPlanetMenu.tscn" id="5_i8xa1"]

[sub_resource type="CircleShape2D" id="CircleShape2D_mtr83"]
radius = 48.0416

[sub_resource type="Animation" id="Animation_fy6yj"]
length = 0.001
tracks/0/type = "bezier"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("../AnimatedSprite2D:material:shader_parameter/flash_value")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(0, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/1/type = "bezier"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("../AnimatedSprite2D:material:shader_parameter/flash_color:r")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/2/type = "bezier"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("../AnimatedSprite2D:material:shader_parameter/flash_color:g")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/3/type = "bezier"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("../AnimatedSprite2D:material:shader_parameter/flash_color:b")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/4/type = "bezier"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("../AnimatedSprite2D:material:shader_parameter/flash_color:a")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}

[sub_resource type="Animation" id="Animation_6fmou"]
resource_name = "blink"
length = 1.5
loop_mode = 1
tracks/0/type = "bezier"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("../AnimatedSprite2D:material:shader_parameter/flash_value")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"handle_modes": PackedInt32Array(0, 0, 0, 0, 0, 0),
"points": PackedFloat32Array(0, -0.25, 0, 0.25, 0, 0.5, -0.25, 0, 0.25, 0, 0, -0.25, 0, 0.25, 0, 0, -0.25, 0, 0.25, 0, 0.25, -0.25, 0, 0.25, 0, 0, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0, 0.0333333, 0.4, 0.5, 0.533333, 0.9)
}
tracks/1/type = "bezier"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("../AnimatedSprite2D:material:shader_parameter/flash_color:r")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/2/type = "bezier"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("../AnimatedSprite2D:material:shader_parameter/flash_color:g")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/3/type = "bezier"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("../AnimatedSprite2D:material:shader_parameter/flash_color:b")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/4/type = "bezier"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("../AnimatedSprite2D:material:shader_parameter/flash_color:a")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}

[sub_resource type="Animation" id="Animation_658sw"]
resource_name = "locked"
length = 0.001
loop_mode = 2
tracks/0/type = "bezier"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("../AnimatedSprite2D:material:shader_parameter/flash_value")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(0.5, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/1/type = "bezier"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("../AnimatedSprite2D:material:shader_parameter/flash_color:r")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(0, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/2/type = "bezier"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("../AnimatedSprite2D:material:shader_parameter/flash_color:g")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(0, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/3/type = "bezier"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("../AnimatedSprite2D:material:shader_parameter/flash_color:b")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(0, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/4/type = "bezier"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("../AnimatedSprite2D:material:shader_parameter/flash_color:a")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_fy6yj"]
_data = {
&"RESET": SubResource("Animation_fy6yj"),
&"blink": SubResource("Animation_6fmou"),
&"locked": SubResource("Animation_658sw")
}

[sub_resource type="ShaderMaterial" id="ShaderMaterial_i8xa1"]
resource_local_to_scene = true
shader = ExtResource("2_fy6yj")
shader_parameter/flash_color = Color(1, 1, 1, 1)
shader_parameter/flash_value = 0.0

[node name="Planet" type="StaticBody2D"]
input_pickable = true
script = ExtResource("1_1odxm")
instant_activate = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_mtr83")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
root_node = NodePath("../CollisionShape2D")
libraries = {
&"": SubResource("AnimationLibrary_fy6yj")
}

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
material = SubResource("ShaderMaterial_i8xa1")
sprite_frames = ExtResource("3_fy6yj")
script = ExtResource("5_dypwo")

[node name="SelectedPlanetMenu" parent="." instance=ExtResource("5_i8xa1")]
visible = false

[connection signal="pressed" from="SelectedPlanetMenu/Background/PlanetInfo/GoToPlanetButton" to="." method="_on_go_to_planet_button_pressed"]

[editable path="SelectedPlanetMenu"]
