class_name UnlockableNode
extends StaticBody2D

signal node_state_changed(new_state: NodeState.Enum)

const CONNECTION_LINE: PackedScene = preload("res://Entities/NodeMap/node_connection.tscn")
@onready var animation_player: AnimationPlayer = $AnimationPlayer

@export var linked_nodes: Array[Node2D]
@export var node_state := NodeState.Enum.LOCKED

@export var require_all := false

@export var outgoing_links: Array[NodeConnection]
@export var incoming_links: Array[NodeConnection]

@export var instant_activate: bool = false

func set_state(new_state: NodeState.Enum, force: bool = false) -> void:
	print("%s state changed to %s" % [self.name, NodeState.to_name(new_state)])

	if new_state < node_state:
		return

	if new_state == node_state and not force:
		return

	match new_state:
		NodeState.Enum.LOCKED:
			animation_player.play(&"locked")
		NodeState.Enum.UNLOCKED:
			animation_player.play(&"blink")
		NodeState.Enum.BEATEN:
			animation_player.play(&"RESET")
			for line in outgoing_links:
				if instant_activate:
					line.set_active()
				else:
					line.activate()

	node_state = new_state
	node_state_changed.emit(new_state)


func _create_links() -> void:
	for child: Node2D in linked_nodes:
		var node := child as UnlockableNode
		if node == null:
			push_error("Linked node is not a UnlockableNode.")
			continue

		var scene := CONNECTION_LINE.instantiate()
		var line  := scene as NodeConnection
		if line == null:
			push_error("Missing NodeConnection in scene.")
			continue

		self.add_child(scene)
		# We need to process connection after all conections has been loaded to scene
		line.initialize(self, node)
		line.connection_activated.connect(node.check_unlock_condition)


func _ready() -> void:
	var data := SaveManager.get_global_data().get_unlockable_node_data().node_states
	var key := SaveManager.get_component_save_name(self)
	node_state = data.get_or_add(key,node_state)
	
	node_state_changed.connect(
		func(new_state: NodeState.Enum) -> void:
			data.set(key, new_state)
	)
	
	_create_links()
	set_state.call_deferred(node_state, true)
	

func check_unlock_condition() -> void:
	if require_all:
		for link in incoming_links:
			if not link.is_active:
				return
	
	if node_state == NodeState.Enum.LOCKED:
		set_state(NodeState.Enum.UNLOCKED)
