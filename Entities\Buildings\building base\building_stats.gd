## Stats of a building
class_name BuildingStats
extends Resource

@export_category("Logic Related Data")
## Experimental key to the localization dictionary
@export var translation_key: String
## Dimensions of a building.
@export var dimensions: Vector2i = Vector2i.ONE
## Whether the building (namely, the sprite) is rotateable.
@export var is_rotateable: bool = false
## The type of a building
@export var building_type: BuildingType.Enum
## Script of various components that are going to be added as nodes to the components of buildings.
@export var components: Array[Resource]

@export var cost : Dictionary[ItemType.Enum,int]

@export_category("Other Resources")
## Texture that should be bound to a sprite at runtime.[br]
## This value is [b]required[\b].
@export var texture: Texture2D
## Collistion shape that shall be bound at runtime.
@export var collision_shape: Shape2D
## Animation that shall be bound at runtime.
@export var animation_frames: SpriteFrames = null
## Script related to [class Building].[br]
## This value is [b]required[\b]
@export var building_script: Script = null

@export_category("Unlock system")
@export var unlocked_by_default: bool = false

@export_category("HUD system")
## Higher value will be added to right side of bar
@export var menu_order_priority: int = 0
