[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=9 format=3 uid="uid://cfyixy0lwgqpy"]

[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="1_fvqff"]
[ext_resource type="SpriteFrames" uid="uid://fw6adbajlg0s" path="res://Assets/Sprites/Animations/64x64/Assembler.tres" id="1_if8wn"]
[ext_resource type="Script" uid="uid://cvwul46h3rvfx" path="res://Entities/Buildings/assembler/assembler.gd" id="2_0845m"]
[ext_resource type="Texture2D" uid="uid://7pcwr3stjf1" path="res://Assets/Sprites/32x32/SpriteSheets/Assembler_sprite_sheet_anim.png" id="2_n20gt"]
[ext_resource type="Script" uid="uid://dfrsc76sgl6kk" path="res://Entities/Components/Power/power_consumer_component.gd" id="3_p3xxg"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="4_etr1y"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_if8wn"]

[sub_resource type="AtlasTexture" id="AtlasTexture_0845m"]
atlas = ExtResource("2_n20gt")
region = Rect2(0, 0, 64, 64)

[resource]
resource_name = "Assembler"
script = ExtResource("1_fvqff")
input_directions = 0
output_directions = 0
transport_speed = 0.0
translation_key = ""
dimensions = Vector2i(2, 2)
is_rotateable = false
building_type = 11
components = Array[Resource]([ExtResource("3_p3xxg"), ExtResource("4_etr1y")])
cost = Dictionary[int, int]({})
texture = SubResource("AtlasTexture_0845m")
collision_shape = SubResource("RectangleShape2D_if8wn")
animation_frames = ExtResource("1_if8wn")
building_script = ExtResource("2_0845m")
unlocked_by_default = true
menu_order_priority = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"
