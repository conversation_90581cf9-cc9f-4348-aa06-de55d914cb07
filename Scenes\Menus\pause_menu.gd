extends Control

func resume():
	get_tree().paused = false
	$AnimationPlayer.play_backwards("blur")
	await $AnimationPlayer.animation_finished
	hide()


func pause(): 
	get_tree().paused = true
	show()
	$AnimationPlayer.play("blur")


func check_escape():
	if Input.is_action_just_pressed("escape") and not get_tree().paused:
		pause()
	elif Input.is_action_just_pressed("escape") and get_tree().paused:
		resume()


func _ready() -> void:
	$AnimationPlayer.play("RESET")
	hide()


func _unhandled_input(_event: InputEvent) -> void:
	check_escape()


func _on_resume_pressed() -> void:
	_play_button_click_sound()
	resume()


func _on_back_to_planet_select_pressed() -> void:
	_play_button_click_sound()
	resume()
	SceneManager.to_space_map()


func _on_quit_pressed() -> void:
	_play_button_click_sound()
	SaveManager.selected_global_name = ""
	get_tree().quit()


func _play_button_click_sound() -> void:
	AudioManager.create_audio(SoundEffect.SOUND_EFFECT_TYPE.UI_BUTTON_CLICKED)
